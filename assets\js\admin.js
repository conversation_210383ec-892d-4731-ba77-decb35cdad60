/**
 * Script JavaScript per la parte amministrativa del plugin
 */
(function($) {
    'use strict';

    // Inizializza il plugin quando il documento è pronto
    $(document).ready(function() {
        // Inizializza l'autocomplete per la ricerca dei prodotti
        initProductSearch();
        
        // Inizializza la gestione degli eventi per i prodotti selezionati
        initSelectedProductsEvents();
        
        // Aggiorna il campo nascosto prima dell'invio del form
        initFormSubmitHandler();
    });

    /**
     * Inizializza l'autocomplete per la ricerca dei prodotti
     */
    function initProductSearch() {
        $('#product_accessories_search').autocomplete({
            source: function(request, response) {
                $.ajax({
                    url: wpaj_admin_params.ajax_url,
                    dataType: 'json',
                    data: {
                        term: request.term,
                        action: 'wpaj_search_products',
                        nonce: wpaj_admin_params.nonce
                    },
                    success: function(data) {
                        if (data.length === 0) {
                            response([{
                                label: wpaj_admin_params.i18n.no_products,
                                value: '',
                                disabled: true
                            }]);
                        } else {
                            response(data);
                        }
                    },
                    error: function() {
                        response([{
                            label: wpaj_admin_params.i18n.error,
                            value: '',
                            disabled: true
                        }]);
                    }
                });
            },
            minLength: 2,
            select: function(event, ui) {
                // Previeni la selezione se l'elemento è disabilitato
                if (ui.item.disabled) {
                    event.preventDefault();
                    return;
                }
                
                // Aggiungi il prodotto selezionato alla lista
                addSelectedProduct(ui.item);
                
                // Resetta il campo di ricerca
                setTimeout(function() {
                    $('#product_accessories_search').val('');
                }, 100);
                
                return false;
            },
            focus: function(event, ui) {
                // Previeni il focus se l'elemento è disabilitato
                if (ui.item.disabled) {
                    event.preventDefault();
                }
                return false;
            }
        }).data('ui-autocomplete')._renderItem = function(ul, item) {
            // Previeni la selezione se l'elemento è disabilitato
            if (item.disabled) {
                return $('<li class="ui-state-disabled">')
                    .append($('<div>').text(item.label))
                    .appendTo(ul);
            }
            
            // Renderizza l'elemento con immagine e prezzo
            var $li = $('<li>');
            var $wrapper = $('<div class="ui-menu-item-wrapper">');
            
            $wrapper.append(
                $('<div class="product-suggestion-image">').html(item.image)
            );
            
            var $info = $('<div class="product-suggestion-info">');
            $info.append($('<div class="product-suggestion-name">').text(item.label));
            $info.append($('<div class="product-suggestion-price">').html(item.price));
            
            $wrapper.append($info);
            $li.append($wrapper);
            
            return $li.appendTo(ul);
        };
    }

    /**
     * Aggiunge un prodotto selezionato alla lista
     *
     * @param {Object} product Prodotto da aggiungere
     */
    function addSelectedProduct(product) {
        // Verifica se il prodotto è già stato selezionato
        if ($('#product_accessories_container').find('[data-product-id="' + product.id + '"]').length > 0) {
            return;
        }
        
        // Crea l'elemento HTML per il prodotto selezionato
        var $product = $('<div class="selected-product" data-product-id="' + product.id + '">');
        
        $product.append(
            $('<div class="product-image">').html(product.image)
        );
        
        var $info = $('<div class="product-info">');
        $info.append($('<span class="product-name">').text(product.label));
        $info.append($('<span class="product-price">').html(product.price));
        
        $product.append($info);
        $product.append(
            $('<a href="#" class="remove-product">&times;</a>')
        );
        
        // Aggiungi il prodotto al contenitore
        $('#product_accessories_container').append($product);
        
        // Aggiorna il campo nascosto
        updateHiddenField();
    }

    /**
     * Inizializza la gestione degli eventi per i prodotti selezionati
     */
    function initSelectedProductsEvents() {
        // Gestisci la rimozione di un prodotto
        $('#product_accessories_container').on('click', '.remove-product', function(e) {
            e.preventDefault();
            
            // Rimuovi il prodotto dalla lista
            $(this).closest('.selected-product').remove();
            
            // Aggiorna il campo nascosto
            updateHiddenField();
        });
    }

    /**
     * Aggiorna il campo nascosto con gli ID dei prodotti selezionati
     */
    function updateHiddenField() {
        var productIds = [];
        
        // Raccogli gli ID di tutti i prodotti selezionati
        $('#product_accessories_container').find('.selected-product').each(function() {
            productIds.push($(this).data('product-id'));
        });
        
        // Aggiorna il valore del campo nascosto
        $('#_product_accessories').val(productIds.join(','));
    }

    /**
     * Inizializza la gestione dell'invio del form
     */
    function initFormSubmitHandler() {
        $('form#post').on('submit', function() {
            // Aggiorna il campo nascosto prima dell'invio
            updateHiddenField();
        });
    }

})(jQuery); 