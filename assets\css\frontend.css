/**
 * Stili CSS per la parte frontend del plugin
 */

/* Contenitore principale degli accessori */
.product-accessories {
    margin: 20px 0 30px 0;
    padding: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    clear: both;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.product-accessories h2 {
    margin-top: 0;
    margin-bottom: 18px;
    font-size: 1.4em;
    font-weight: 700;
    color: #333;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
    position: relative;
}

.product-accessories h2::after {
    content: 'Clicca per selezionare';
    position: absolute;
    right: 0;
    bottom: 12px;
    font-size: 0.7em;
    font-weight: 400;
    color: #666;
    background: #fff;
    padding: 2px 8px;
    border-radius: 12px;
    border: 1px solid #e0e0e0;
}

/* Gruppi di accessori */
.accessories-group {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.accessories-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accessories-group h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
    font-weight: 500;
    color: #444;
}

.group-description {
    margin-bottom: 12px;
    color: #666;
    font-size: 0.95em;
    line-height: 1.5;
}

/* Lista degli accessori */
.accessories-list {
    display: grid;
    gap: 12px;
}

/* Modalità half-width (default): due prodotti per riga */
.accessories-list-half-width {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Modalità full-width: un prodotto per riga */
.accessories-list-full-width {
    grid-template-columns: 1fr;
}

/* Nascondi le checkbox ma mantienile per la funzionalità */
.visuallyhidden {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

/* Prodotto accessorio */
.accessory-product {
    display: flex;
    align-items: center;
    padding: 16px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    background-color: #fff;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

/* Aggiunge un sottile gradiente di sfondo per indicare interattività */
.accessory-product::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.02) 0%, rgba(33, 150, 243, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 0;
}

.accessory-product:hover {
    border-color: #2196F3;
    box-shadow: 0 4px 20px rgba(33, 150, 243, 0.15), 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px) scale(1.02);
    background-color: #fafafa;
}

.accessory-product:hover::before {
    opacity: 1;
}

/* Effetto focus per accessibilità */
.accessory-product:focus-within {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
}

/* Stile per il prodotto selezionato */
.accessory-product.selected {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.08) 0%, rgba(76, 175, 80, 0.04) 100%);
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25), 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
}

.accessory-product.selected::before {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
    opacity: 1;
}

/* Animazione di selezione con pulse effect */
.accessory-product.selected::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px solid #4CAF50;
    border-radius: 8px;
    opacity: 0;
    animation: selectionPulse 0.6s ease-out;
    pointer-events: none;
}

@keyframes selectionPulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0;
        transform: scale(1.05);
    }
}

/* Quando il prodotto è selezionato */
.accessory-product.selected .accessory-image img {
    border: 2px solid #4CAF50;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.2);
}

.accessory-product.selected .accessory-name {
    color: #2E7D32;
    font-weight: 700;
}

.accessory-product.selected .accessory-price {
    color: #2E7D32;
    font-weight: 600;
}

.accessory-product:active {
    background-color: #f0f0f0;
    transform: translateY(0) scale(0.98);
}

/* Indicatore di selezione migliorato */
.accessory-indicator {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    display: none;
    z-index: 3;
    box-shadow: 0 2px 8px rgba(76, 175, 80, 0.4);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.accessory-product.selected .accessory-indicator {
    display: block;
    animation: checkmarkAppear 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes checkmarkAppear {
    0% {
        opacity: 0;
        transform: scale(0) rotate(180deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(90deg);
    }
    100% {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

.accessory-indicator:after {
    content: '';
    position: absolute;
    top: 6px;
    left: 5px;
    width: 12px;
    height: 6px;
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
    transition: all 0.2s ease;
}

/* Hover effect sull'indicatore */
.accessory-product:hover .accessory-indicator {
    transform: scale(1.1);
    box-shadow: 0 3px 12px rgba(76, 175, 80, 0.5);
}

/* Immagine dell'accessorio migliorata */
.accessory-image {
    flex: 0 0 90px;
    margin-right: 16px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.accessory-image img {
    max-width: 100%;
    height: auto;
    border-radius: 6px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.accessory-product:hover .accessory-image img {
    border-color: #2196F3;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);
    transform: scale(1.05);
}

/* Informazioni dell'accessorio migliorate */
.accessory-info {
    flex: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 1;
}

.accessory-name {
    margin: 0 0 8px 0;
    font-size: 1.05em;
    font-weight: 600;
    line-height: 1.3;
    color: #333;
    transition: all 0.3s ease;
}

.accessory-product:hover .accessory-name {
    color: #1976D2;
}

.accessory-price {
    color: #666;
    font-weight: 500;
    font-size: 0.95em;
    transition: all 0.3s ease;
}

.accessory-price .amount {
    color: #2E7D32;
    font-weight: 700;
    font-size: 1.1em;
}

.accessory-product:hover .accessory-price {
    color: #555;
}

/* Aggiunge un effetto di loading per feedback immediato */
.accessory-product.loading {
    opacity: 0.8;
    pointer-events: none;
    transform: scale(0.98);
}

.accessory-product.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #2196F3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 10;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Stato hover preview per indicare interattività */
.accessory-product.hover-preview {
    border-color: #90CAF9;
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.05) 0%, rgba(33, 150, 243, 0.02) 100%);
    box-shadow: 0 2px 12px rgba(33, 150, 243, 0.1);
}

/* Animazione di deselezione */
.accessory-product.deselecting {
    border-color: #FF9800;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.08) 0%, rgba(255, 152, 0, 0.04) 100%);
    transition: all 0.2s ease;
}

/* Migliora l'indicatore di selezione con stati aggiuntivi */
.accessory-product.hover-preview .accessory-indicator {
    display: block;
    opacity: 0.3;
    background: linear-gradient(135deg, #90CAF9 0%, #42A5F5 100%);
    transform: scale(0.8);
}

/* Effetto di pulsazione per elementi appena selezionati */
.accessory-product.selected.just-selected {
    animation: selectionGlow 1s ease-out;
}

@keyframes selectionGlow {
    0% {
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25), 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    50% {
        box-shadow: 0 6px 30px rgba(76, 175, 80, 0.4), 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    100% {
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25), 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Migliora l'accessibilità con stati di focus */
.accessory-product:focus {
    outline: 3px solid #2196F3;
    outline-offset: 2px;
    border-color: #2196F3;
    box-shadow: 0 4px 20px rgba(33, 150, 243, 0.2), 0 2px 8px rgba(0, 0, 0, 0.1);
}

.accessory-checkbox-input:focus + .accessory-image,
.accessory-checkbox-input:focus ~ .accessory-info {
    outline: 2px solid #2196F3;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Stato di focus visibile per la navigazione da tastiera */
.accessory-product:focus-visible {
    outline: 3px solid #2196F3;
    outline-offset: 3px;
    border-color: #2196F3;
}

/* Rimuove l'outline per i click del mouse ma lo mantiene per la tastiera */
.accessory-product:focus:not(:focus-visible) {
    outline: none;
}

/* Stili per dispositivi mobili migliorati */
@media (max-width: 768px) {
    .accessories-list {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .accessory-product {
        padding: 14px;
        flex-direction: row;
        align-items: center;
    }

    .accessory-image {
        flex: 0 0 80px;
        margin-right: 14px;
    }

    .accessory-indicator {
        top: 10px;
        right: 10px;
        width: 20px;
        height: 20px;
    }

    .accessory-indicator:after {
        top: 4px;
        left: 4px;
        width: 10px;
        height: 5px;
    }

    .accessory-name {
        font-size: 1em;
    }

    .accessory-price {
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .product-accessories {
        padding: 16px;
        margin: 15px 0 25px 0;
    }

    .product-accessories h2 {
        font-size: 1.2em;
        margin-bottom: 15px;
    }

    .product-accessories h2::after {
        display: none; /* Nascondi il suggerimento su mobile per risparmiare spazio */
    }

    .accessory-product {
        padding: 12px;
        border-radius: 6px;
    }

    .accessory-image {
        flex: 0 0 70px;
        margin-right: 12px;
    }

    .accessory-name {
        font-size: 0.95em;
        margin-bottom: 6px;
        line-height: 1.2;
    }

    .accessory-price {
        font-size: 0.85em;
    }

    .accessory-indicator {
        top: 8px;
        right: 8px;
        width: 18px;
        height: 18px;
    }

    .accessory-indicator:after {
        top: 3px;
        left: 3px;
        width: 8px;
        height: 4px;
    }

    /* Migliora il feedback tattile su mobile */
    .accessory-product:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
}

/* Aggiunge supporto per dispositivi con hover limitato (touch) */
@media (hover: none) and (pointer: coarse) {
    .accessory-product:hover {
        transform: none;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .accessory-product:hover::before {
        opacity: 0;
    }

    .accessory-product:hover .accessory-image img {
        transform: none;
        border-color: #f0f0f0;
    }

    .accessory-product:hover .accessory-name {
        color: #333;
    }

    /* Mantieni solo gli effetti di selezione */
    .accessory-product.selected:hover {
        border-color: #4CAF50;
        box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25), 0 2px 8px rgba(0, 0, 0, 0.1);
    }
}

/* Messaggio di successo */
.wpaj-success-message {
    padding: 10px 15px;
    margin: 10px 0;
    background-color: #f0f9eb;
    color: #67c23a;
    border-radius: 4px;
    border-left: 4px solid #67c23a;
}

/* Messaggio di errore */
.wpaj-error-message {
    padding: 10px 15px;
    margin: 10px 0;
    background-color: #fef0f0;
    color: #f56c6c;
    border-radius: 4px;
    border-left: 4px solid #f56c6c;
}

/* Stile per l'etichetta degli accessori nel carrello */
.wpaj-accessory-label {
    display: inline-block;
    background-color: #f7f7f7;
    color: #666;
    font-size: 0.85em;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 5px;
    font-weight: normal;
}

/* Compatibilità con la modalità di archiviazione ad alte prestazioni */
.woocommerce-order-details .wpaj-accessory-label {
    background-color: #f0f0f0;
} 