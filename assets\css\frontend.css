/**
 * Stili CSS per la parte frontend del plugin
 */

/* Contenitore principale degli accessori */
.product-accessories {
    margin: 20px 0 30px 0;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    background-color: #f9f9f9;
    clear: both;
}

.product-accessories h2 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

/* Gruppi di accessori */
.accessories-group {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #ddd;
}

.accessories-group:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.accessories-group h3 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
    font-weight: 500;
    color: #444;
}

.group-description {
    margin-bottom: 12px;
    color: #666;
    font-size: 0.95em;
    line-height: 1.5;
}

/* Lista degli accessori */
.accessories-list {
    display: grid;
    gap: 12px;
}

/* Modalità half-width (default): due prodotti per riga */
.accessories-list-half-width {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Modalità full-width: un prodotto per riga */
.accessories-list-full-width {
    grid-template-columns: 1fr;
}

/* Nascondi le checkbox ma mantienile per la funzionalità */
.visuallyhidden {
    position: absolute;
    overflow: hidden;
    clip: rect(0 0 0 0);
    height: 1px;
    width: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

/* Prodotto accessorio */
.accessory-product {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    background-color: #fff;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.accessory-product:hover {
    border-color: #ccc;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

/* Stile per il prodotto selezionato */
.accessory-checkbox-input:checked + .accessory-image + .accessory-info + .accessory-indicator,
.accessory-checkbox-input:checked ~ .accessory-indicator {
    display: block;
}

.accessory-checkbox-input:checked ~ .accessory-image,
.accessory-checkbox-input:checked ~ .accessory-info {
    opacity: 1;
}

.accessory-checkbox-input:checked + .accessory-image {
    opacity: 1;
}

.accessory-product.selected,
.accessory-checkbox-input:checked ~ .accessory-info,
.accessory-checkbox-input:checked + .accessory-image,
.accessory-checkbox-input:checked {
    border-color: #4CAF50;
}

/* Quando il prodotto è selezionato */
.accessory-checkbox-input:checked ~ .accessory-image img {
    border: 1px solid #4CAF50;
}

.accessory-checkbox-input:checked ~ .accessory-info .accessory-name {
    color: #4CAF50;
}

.accessory-checkbox-input:checked + .accessory-image + .accessory-info + .accessory-indicator {
    display: block;
}

.accessory-product:active {
    background-color: #f5f5f5;
    transform: translateY(0);
}

/* Indicatore di selezione */
.accessory-indicator {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background-color: #4CAF50;
    display: none;
    z-index: 2;
}

.accessory-indicator:after {
    content: '';
    position: absolute;
    top: 4px;
    left: 3px;
    width: 10px;
    height: 5px;
    border: 2px solid #fff;
    border-top: none;
    border-right: none;
    transform: rotate(-45deg);
}

/* Prodotto selezionato */
.accessory-checkbox-input:checked ~ .accessory-product,
.accessory-product.selected {
    border-color: #4CAF50;
    box-shadow: 0 3px 10px rgba(76, 175, 80, 0.2);
}

/* Immagine dell'accessorio */
.accessory-image {
    flex: 0 0 80px;
    margin-right: 12px;
    transition: all 0.2s ease;
}

.accessory-image img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    border: 1px solid #eee;
    transition: border-color 0.2s ease;
}

/* Informazioni dell'accessorio */
.accessory-info {
    flex: 1;
    transition: all 0.2s ease;
}

.accessory-name {
    margin: 0 0 5px 0;
    font-size: 1em;
    font-weight: 600;
    transition: color 0.2s ease;
}

.accessory-price {
    color: #777;
    font-weight: 500;
}

.accessory-price .amount {
    color: #333;
}

/* Stili per dispositivi mobili */
@media (max-width: 768px) {
    .accessories-list {
        grid-template-columns: 1fr;
    }
    
    .accessory-product {
        flex-wrap: wrap;
    }
    
    .accessory-image {
        flex: 0 0 70px;
    }
}

/* Messaggio di successo */
.wpaj-success-message {
    padding: 10px 15px;
    margin: 10px 0;
    background-color: #f0f9eb;
    color: #67c23a;
    border-radius: 4px;
    border-left: 4px solid #67c23a;
}

/* Messaggio di errore */
.wpaj-error-message {
    padding: 10px 15px;
    margin: 10px 0;
    background-color: #fef0f0;
    color: #f56c6c;
    border-radius: 4px;
    border-left: 4px solid #f56c6c;
}

/* Stile per l'etichetta degli accessori nel carrello */
.wpaj-accessory-label {
    display: inline-block;
    background-color: #f7f7f7;
    color: #666;
    font-size: 0.85em;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 5px;
    font-weight: normal;
}

/* Compatibilità con la modalità di archiviazione ad alte prestazioni */
.woocommerce-order-details .wpaj-accessory-label {
    background-color: #f0f0f0;
} 