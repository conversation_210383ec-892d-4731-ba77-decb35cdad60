/**
 * Script JavaScript per la parte frontend del plugin
 */
(function($) {
    'use strict';

    // Inizializza il plugin quando il documento è pronto
    $(document).ready(function() {
        // Inizializza la gestione del pulsante "Aggiungi al carrello"
        initAddToCartButton();

        // Inizializza la gestione delle checkbox degli accessori
        initAccessoryCheckboxes();

        // Aggiungi animazione di entrata per gli accessori
        initAccessoriesEntryAnimation();

        // Debug: Verifica che i parametri siano disponibili
        // console.log('WPAJ Frontend Params:', typeof wpaj_frontend_params !== 'undefined' ? 'Disponibili' : 'Non disponibili');
    });

    /**
     * Inizializza l'animazione di entrata per la sezione accessori
     */
    function initAccessoriesEntryAnimation() {
        var $accessories = $('.product-accessories');
        if ($accessories.length > 0) {
            // Nascondi inizialmente gli accessori
            $accessories.css({
                'opacity': '0',
                'transform': 'translateY(20px)'
            });

            // Anima l'entrata dopo un breve ritardo
            setTimeout(function() {
                $accessories.animate({
                    'opacity': 1
                }, 600).css({
                    'transform': 'translateY(0)',
                    'transition': 'transform 0.6s cubic-bezier(0.4, 0, 0.2, 1)'
                });

                // Anima l'entrata di ogni prodotto accessorio
                $('.accessory-product').each(function(index) {
                    var $product = $(this);
                    $product.css({
                        'opacity': '0',
                        'transform': 'translateY(10px)'
                    });

                    setTimeout(function() {
                        $product.animate({
                            'opacity': 1
                        }, 400).css({
                            'transform': 'translateY(0)',
                            'transition': 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)'
                        });
                    }, index * 100 + 200);
                });
            }, 300);
        }
    }

    /**
     * Inizializza la gestione del pulsante "Aggiungi al carrello"
     */
    function initAddToCartButton() {
        // Intercetta il click sul pulsante "Aggiungi al carrello"
        $('.single_add_to_cart_button').on('click', function(e) {
            // Verifica se ci sono accessori disponibili
            if ($('.accessory-checkbox-input').length === 0) {
                return;
            }
            
            // Previeni il comportamento predefinito
            e.preventDefault();
            
            // Ottieni l'ID del prodotto principale
            var productId = $('input[name="add-to-cart"]').val();
            if (!productId) {
                productId = $('button[name="add-to-cart"]').val();
            }
            
            // Debug: Verifica l'ID del prodotto
            // console.log('Product ID:', productId);
            
            // Ottieni la quantità del prodotto principale
            var quantity = $('input[name="quantity"]').val();
            if (!quantity) {
                quantity = 1;
            }
            
            // Debug: Verifica la quantità
            // console.log('Quantity:', quantity);
            
            // Ottieni gli accessori selezionati
            var accessories = [];
            $('.accessory-checkbox-input:checked').each(function() {
                accessories.push($(this).val());
            });
            
            // Debug: Verifica gli accessori selezionati
            // console.log('Accessories:', accessories);
            
            // Aggiungi i prodotti al carrello
            addProductsToCart(productId, quantity, accessories);
        });
    }

    /**
     * Inizializza la gestione delle checkbox degli accessori
     */
    function initAccessoryCheckboxes() {
        // Gestisci il click sulle checkbox
        $('.accessory-checkbox-input').on('change', function() {
            updateTotalPrice();

            // Aggiorna lo stato visuale del prodotto con animazioni
            var $product = $(this).closest('.accessory-product');
            var $checkbox = $(this);

            if ($checkbox.is(':checked')) {
                selectAccessoryWithAnimation($product);
            } else {
                deselectAccessoryWithAnimation($product);
            }
        });

        // Gestisci il click sull'intero div del prodotto accessorio
        $('.accessory-product').on('click', function(e) {
            // Previeni il doppio click se si clicca direttamente sulla checkbox
            if ($(e.target).hasClass('accessory-checkbox-input')) {
                return;
            }

            // Ottieni la checkbox corrispondente
            var $checkbox = $(this).find('.accessory-checkbox-input');
            var $product = $(this);

            // Aggiungi feedback visivo immediato
            $product.addClass('loading');

            // Cambia lo stato della checkbox con un piccolo ritardo per l'animazione
            setTimeout(function() {
                $checkbox.prop('checked', !$checkbox.prop('checked')).trigger('change');
                $product.removeClass('loading');
            }, 150);
        });

        // Previeni la propagazione degli eventi quando si fa click sulla checkbox
        $('.accessory-checkbox-input').on('click', function(e) {
            e.stopPropagation();
        });

        // Aggiungi effetti hover migliorati
        $('.accessory-product').on('mouseenter', function() {
            if (!$(this).hasClass('selected')) {
                $(this).addClass('hover-preview');
            }
        }).on('mouseleave', function() {
            $(this).removeClass('hover-preview');
        });

        // Aggiungi supporto per la navigazione da tastiera
        $('.accessory-product').on('keydown', function(e) {
            // Spazio o Enter per selezionare/deselezionare
            if (e.which === 32 || e.which === 13) {
                e.preventDefault();
                $(this).click();
            }
        });

        // Aggiorna gli attributi ARIA quando lo stato cambia
        $('.accessory-checkbox-input').on('change', function() {
            var $product = $(this).closest('.accessory-product');
            var isChecked = $(this).is(':checked');

            $product.attr('aria-pressed', isChecked ? 'true' : 'false');
        });

        // Imposta lo stato visuale iniziale per gli accessori già selezionati
        $('.accessory-checkbox-input:checked').each(function() {
            $(this).closest('.accessory-product').addClass('selected');
        });

        // Aggiorna il prezzo totale all'avvio
        updateTotalPrice();
    }

    /**
     * Seleziona un accessorio con animazione
     */
    function selectAccessoryWithAnimation($product) {
        // Rimuovi eventuali classi di stato precedenti
        $product.removeClass('hover-preview deselecting');

        // Aggiungi la classe selected con un piccolo ritardo per l'animazione
        setTimeout(function() {
            $product.addClass('selected');

            // Aggiungi un effetto di feedback visivo
            showSelectionFeedback($product, 'selected');
        }, 50);
    }

    /**
     * Deseleziona un accessorio con animazione
     */
    function deselectAccessoryWithAnimation($product) {
        // Aggiungi classe temporanea per l'animazione di deselezione
        $product.addClass('deselecting');

        // Rimuovi la classe selected dopo un breve ritardo
        setTimeout(function() {
            $product.removeClass('selected deselecting');

            // Aggiungi un effetto di feedback visivo
            showSelectionFeedback($product, 'deselected');
        }, 200);
    }

    /**
     * Mostra feedback visivo per la selezione/deselezione
     */
    function showSelectionFeedback($product, action) {
        var message = action === 'selected' ? 'Aggiunto' : 'Rimosso';
        var color = action === 'selected' ? '#4CAF50' : '#FF9800';

        // Crea un elemento di feedback temporaneo
        var $feedback = $('<div class="selection-feedback">')
            .text(message)
            .css({
                'position': 'absolute',
                'top': '50%',
                'left': '50%',
                'transform': 'translate(-50%, -50%)',
                'background': color,
                'color': 'white',
                'padding': '4px 8px',
                'border-radius': '4px',
                'font-size': '12px',
                'font-weight': 'bold',
                'z-index': '1000',
                'opacity': '0',
                'pointer-events': 'none'
            });

        // Aggiungi il feedback al prodotto
        $product.css('position', 'relative').append($feedback);

        // Anima il feedback
        $feedback.animate({
            'opacity': 1,
            'top': '40%'
        }, 200).delay(800).animate({
            'opacity': 0,
            'top': '30%'
        }, 200, function() {
            $feedback.remove();
        });
    }

    /**
     * Aggiorna il prezzo totale in base agli accessori selezionati
     */
    function updateTotalPrice() {
        // Implementazione futura per mostrare il prezzo totale
        var totalAccessories = $('.accessory-checkbox-input:checked').length;

        // Aggiorna il contatore degli accessori selezionati se esiste
        var $counter = $('.accessories-selected-counter');
        if ($counter.length === 0 && totalAccessories > 0) {
            // Crea un contatore se non esiste
            $counter = $('<div class="accessories-selected-counter">')
                .css({
                    'position': 'fixed',
                    'bottom': '20px',
                    'right': '20px',
                    'background': '#2196F3',
                    'color': 'white',
                    'padding': '8px 12px',
                    'border-radius': '20px',
                    'font-size': '14px',
                    'font-weight': 'bold',
                    'box-shadow': '0 2px 8px rgba(33, 150, 243, 0.3)',
                    'z-index': '1000',
                    'transition': 'all 0.3s ease'
                })
                .appendTo('body');
        }

        if (totalAccessories > 0) {
            $counter.text(totalAccessories + ' accessori selezionati').show();
        } else {
            $counter.hide();
        }
    }

    /**
     * Aggiunge i prodotti al carrello via AJAX
     *
     * @param {int} productId ID del prodotto principale
     * @param {int} quantity Quantità del prodotto principale
     * @param {Array} accessories Array di ID degli accessori selezionati
     */
    function addProductsToCart(productId, quantity, accessories) {
        // Verifica che i parametri siano validi
        if (!productId) {
            alert('Errore: Prodotto non valido');
            return;
        }
        
        // Verifica che wpaj_frontend_params sia definito
        if (typeof wpaj_frontend_params === 'undefined') {
            alert('Errore: Parametri non disponibili');
            return;
        }
        
        // Mostra un indicatore di caricamento
        var $button = $('.single_add_to_cart_button');
        var buttonText = $button.text();
        $button.addClass('loading').text('...');
        
        // Rimuovi eventuali messaggi precedenti
        $('.wpaj-message').remove();
        
        // Raccoglie i dati di assicurazione, se presenti
        var insuranceData = {};
        
        // Verifica in primo luogo se c'è un'assicurazione selezionata tramite checkbox
        var $insuranceCheckbox = $('.wc-product-insurance-option-card input[type=checkbox]:checked');
        if ($insuranceCheckbox.length > 0) {
            insuranceData.insurance_id = $insuranceCheckbox.val();
            // console.log('Insurance ID (from checkbox):', insuranceData.insurance_id);
        } 
        // In alternativa cerca l'input hidden con il nome insurance_id
        else {
            var $insuranceInput = $('input[name="insurance_id"]');
            if ($insuranceInput.length > 0 && $insuranceInput.val()) {
                insuranceData.insurance_id = $insuranceInput.val();
                // console.log('Insurance ID (from hidden input):', insuranceData.insurance_id);
            }
            // Come ultima risorsa, controlla nel localStorage
            else if (localStorage.getItem('wc_insurance_selected') || localStorage.getItem('wc_insurance_selected_temp')) {
                insuranceData.insurance_id = localStorage.getItem('wc_insurance_selected') || localStorage.getItem('wc_insurance_selected_temp');
                // console.log('Insurance ID (from localStorage):', insuranceData.insurance_id);
                
                // Rimuovi il valore temporaneo dopo averlo usato
                if (localStorage.getItem('wc_insurance_selected_temp')) {
                    localStorage.removeItem('wc_insurance_selected_temp');
                }
            }
        }
        
        // Prepara i dati per la richiesta AJAX
        var requestData = {
            action: 'wpaj_add_accessories_to_cart',
            product_id: productId,
            quantity: quantity,
            accessories: accessories,
            nonce: wpaj_frontend_params.nonce
        };
        
        // Aggiungi l'ID dell'assicurazione se presente
        if (insuranceData.insurance_id) {
            requestData.insurance_id = insuranceData.insurance_id;
        }
        
        // Debug: mostra i dati che verranno inviati
        // console.log('AJAX request data:', requestData);
        
        // Invia la richiesta AJAX
        $.ajax({
            url: wpaj_frontend_params.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: requestData,
            success: function(response) {
                // Ripristina il pulsante
                $button.removeClass('loading').text(buttonText);
                
                if (response.success) {
                    // Mostra un messaggio di successo
                    $('<div class="wpaj-message wpaj-success-message">')
                        .text(response.message)
                        .insertBefore('.product-accessories');
                    
                    // Verifica se è necessario reindirizzare al carrello
                    if (wpaj_frontend_params.redirect_to_cart && response.redirect) {
                        // Reindirizza al carrello dopo un breve ritardo
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1000);
                    } else {
                        // Soluzione semplice: ricarica la pagina
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    }
                } else {
                    // Mostra un messaggio di errore
                    $('<div class="wpaj-message wpaj-error-message">')
                        .text(response.message)
                        .insertBefore('.product-accessories');
                }
            },
            error: function(xhr, status, error) {
                // Ripristina il pulsante
                $button.removeClass('loading').text(buttonText);
                
                // Mostra un messaggio di errore
                $('<div class="wpaj-message wpaj-error-message">')
                    .text(wpaj_frontend_params.i18n.error)
                    .insertBefore('.product-accessories');
                
                // console.error('AJAX error:', xhr, status, error);
            }
        });
    }

})(jQuery); 