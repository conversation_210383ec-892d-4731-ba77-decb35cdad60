<?php
/**
 * Classe per la gestione dei gruppi di prodotti accessori
 *
 * @package Woo_Product_Addons_Jojo
 * @since 1.0.0
 */

// Impedisce l'accesso diretto al file
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Classe WPAJ_Groups
 */
class WPAJ_Groups {

    /**
     * Nome dell'opzione per i gruppi di accessori
     */
    private $option_name = 'wpaj_accessory_groups';

    /**
     * Nome dell'opzione per le impostazioni
     */
    private $settings_option_name = 'wpaj_settings';

    /**
     * Costruttore
     */
    public function __construct() {
        // Aggiungi voce di menu per la pagina di impostazioni
        add_action('admin_menu', array($this, 'add_menu_page'));
        
        // Registra le impostazioni
        add_action('admin_init', array($this, 'register_settings'));
        
        // Aggiungi script e stili per l'admin
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Aggiungi endpoint AJAX per la ricerca dei prodotti
        add_action('wp_ajax_wpaj_search_products_for_group', array($this, 'ajax_search_products'));
        
        // Aggiungi endpoint AJAX per salvare un gruppo
        add_action('wp_ajax_wpaj_save_accessory_group', array($this, 'ajax_save_group'));
        
        // Aggiungi endpoint AJAX per eliminare un gruppo
        add_action('wp_ajax_wpaj_delete_accessory_group', array($this, 'ajax_delete_group'));
        
        // Aggiungi endpoint AJAX per ottenere i dettagli di un gruppo
        add_action('wp_ajax_wpaj_get_accessory_group', array($this, 'ajax_get_group'));
        
        // Aggiungi endpoint AJAX per ottenere i dettagli dei prodotti
        add_action('wp_ajax_wpaj_get_products_details', array($this, 'ajax_get_products_details'));
        
        // Aggiungi handler per il form POST
        add_action('admin_post_wpaj_save_group_form', array($this, 'handle_save_group_form'));
    }

    /**
     * Aggiunge la voce di menu per la pagina di impostazioni
     */
    public function add_menu_page() {
        add_submenu_page(
            'edit.php?post_type=product',
            __('Prodotti Accessori', 'woo-product-addons-jojo'),
            __('Prodotti Accessori', 'woo-product-addons-jojo'),
            'manage_woocommerce',
            'wpaj-accessory-groups',
            array($this, 'render_groups_page')
        );
        
        // Aggiungi la pagina delle impostazioni come pagina nascosta
        add_submenu_page(
            null, // Non mostrare nel menu
            __('Impostazioni Accessori', 'woo-product-addons-jojo'),
            __('Impostazioni Accessori', 'woo-product-addons-jojo'),
            'manage_woocommerce',
            'wpaj-accessory-settings',
            array($this, 'render_settings_page')
        );
    }

    /**
     * Registra le impostazioni
     */
    public function register_settings() {
        // Registra le impostazioni per i gruppi
        register_setting(
            'wpaj_accessory_groups_option_group',
            $this->option_name,
            array($this, 'sanitize_groups')
        );

        // Registra le impostazioni generali
        register_setting(
            'wpaj_settings_option_group',
            $this->settings_option_name,
            array($this, 'sanitize_settings')
        );
    }

    /**
     * Sanitizza i dati dei gruppi
     *
     * @param array $input I dati da sanitizzare
     * @return array I dati sanitizzati
     */
    public function sanitize_groups($input) {
        if (!is_array($input)) {
            return array();
        }

        $sanitized_input = array();

        foreach ($input as $group_id => $group) {
            $sanitized_input[$group_id] = array(
                'name' => sanitize_text_field($group['name']),
                'description' => wp_kses_post($group['description']),
                'categories' => isset($group['categories']) ? array_map('absint', $group['categories']) : array(),
                'accessories' => isset($group['accessories']) ? array_map('absint', $group['accessories']) : array(),
                'min_price' => isset($group['min_price']) ? floatval($group['min_price']) : '',
                'max_price' => isset($group['max_price']) ? floatval($group['max_price']) : '',
                'display_mode' => isset($group['display_mode']) && in_array($group['display_mode'], array('half-width', 'full-width')) ? $group['display_mode'] : 'half-width'
            );
        }

        return $sanitized_input;
    }

    /**
     * Sanitizza le impostazioni generali
     *
     * @param array $input I dati da sanitizzare
     * @return array I dati sanitizzati
     */
    public function sanitize_settings($input) {
        $sanitized_input = array();

        // Sanitizza il campo redirect_to_cart
        $sanitized_input['redirect_to_cart'] = isset($input['redirect_to_cart']) ? 1 : 0;

        return $sanitized_input;
    }

    /**
     * Carica gli script e gli stili per l'admin
     *
     * @param string $hook L'hook della pagina corrente
     */
    public function enqueue_admin_scripts($hook) {
        // Carica gli script solo nelle pagine del plugin
        if ('product_page_wpaj-accessory-groups' !== $hook && 'admin_page_wpaj-accessory-settings' !== $hook) {
            return;
        }

        // Registra e carica lo stile CSS
        wp_register_style(
            'wpaj-groups-style',
            WPAJ_PLUGIN_URL . 'assets/css/groups.css',
            array('wp-jquery-ui-dialog'),
            WPAJ_PLUGIN_VERSION
        );
        wp_enqueue_style('wpaj-groups-style');

        // Registra e carica lo script JS
        wp_register_script(
            'wpaj-groups-script',
            WPAJ_PLUGIN_URL . 'assets/js/groups.js',
            array('jquery', 'jquery-ui-autocomplete', 'jquery-ui-dialog', 'jquery-ui-sortable'),
            WPAJ_PLUGIN_VERSION,
            true
        );

        // Passa i parametri allo script
        wp_localize_script(
            'wpaj-groups-script',
            'wpaj_groups_params',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('wpaj_groups_nonce'),
                'i18n' => array(
                    'no_products' => __('Nessun prodotto trovato', 'woo-product-addons-jojo'),
                    'error' => __('Errore durante la ricerca', 'woo-product-addons-jojo'),
                    'confirm_delete' => __('Sei sicuro di voler eliminare questo gruppo?', 'woo-product-addons-jojo'),
                    'group_saved' => __('Gruppo salvato con successo', 'woo-product-addons-jojo'),
                    'group_deleted' => __('Gruppo eliminato con successo', 'woo-product-addons-jojo')
                )
            )
        );
        wp_enqueue_script('wpaj-groups-script');
    }

    /**
     * Renderizza la pagina dei gruppi
     */
    public function render_groups_page() {
        // Ottieni i gruppi salvati
        $groups = get_option($this->option_name, array());
        
        // Ottieni tutte le categorie di prodotti
        $product_categories = get_terms(array(
            'taxonomy' => 'product_cat',
            'hide_empty' => false,
        ));
        
        // Gestione dei messaggi
        $message = isset($_GET['message']) ? sanitize_text_field($_GET['message']) : '';
        $error = isset($_GET['error']) ? sanitize_text_field($_GET['error']) : '';
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Gestione Prodotti Accessori', 'woo-product-addons-jojo'); ?></h1>
            
            <?php if ($message === 'group_saved') : ?>
                <div class="notice notice-success is-dismissible">
                    <p><?php echo esc_html__('Gruppo salvato con successo.', 'woo-product-addons-jojo'); ?></p>
                </div>
            <?php endif; ?>
            
            <?php if ($error === 'empty_name') : ?>
                <div class="notice notice-error is-dismissible">
                    <p><?php echo esc_html__('Il nome del gruppo è obbligatorio.', 'woo-product-addons-jojo'); ?></p>
                </div>
            <?php endif; ?>
            
            <div class="nav-tab-wrapper">
                <a href="<?php echo admin_url('edit.php?post_type=product&page=wpaj-accessory-groups'); ?>" class="nav-tab nav-tab-active"><?php echo esc_html__('Gruppi di Accessori', 'woo-product-addons-jojo'); ?></a>
                <a href="<?php echo admin_url('edit.php?post_type=product&page=wpaj-accessory-settings'); ?>" class="nav-tab"><?php echo esc_html__('Impostazioni', 'woo-product-addons-jojo'); ?></a>
            </div>
            
            <div class="wpaj-groups-container">
                <div class="wpaj-groups-list">
                    <h2><?php echo esc_html__('Gruppi esistenti', 'woo-product-addons-jojo'); ?></h2>
                    
                    <?php if (empty($groups)) : ?>
                        <p><?php echo esc_html__('Nessun gruppo creato.', 'woo-product-addons-jojo'); ?></p>
                    <?php else : ?>
                        <ul class="wpaj-groups">
                            <?php foreach ($groups as $group_id => $group) : ?>
                                <li class="wpaj-group-item" data-group-id="<?php echo esc_attr($group_id); ?>">
                                    <div class="wpaj-group-header">
                                        <h3><?php echo esc_html($group['name']); ?></h3>
                                        <div class="wpaj-group-actions">
                                            <button type="button" class="button edit-group"><?php echo esc_html__('Modifica', 'woo-product-addons-jojo'); ?></button>
                                            <button type="button" class="button delete-group"><?php echo esc_html__('Elimina', 'woo-product-addons-jojo'); ?></button>
                                        </div>
                                    </div>
                                    <div class="wpaj-group-description">
                                        <?php echo wp_kses_post($group['description']); ?>
                                    </div>
                                    <div class="wpaj-group-categories">
                                        <strong><?php echo esc_html__('Categorie:', 'woo-product-addons-jojo'); ?></strong>
                                        <?php
                                        if (!empty($group['categories'])) {
                                            $category_names = array();
                                            foreach ($group['categories'] as $cat_id) {
                                                $term = get_term($cat_id, 'product_cat');
                                                if ($term && !is_wp_error($term)) {
                                                    $category_names[] = $term->name;
                                                }
                                            }
                                            echo esc_html(implode(', ', $category_names));
                                        } else {
                                            echo esc_html__('Nessuna categoria selezionata', 'woo-product-addons-jojo');
                                        }
                                        ?>
                                    </div>
                                    <div class="wpaj-group-accessories">
                                        <strong><?php echo esc_html__('Accessori:', 'woo-product-addons-jojo'); ?></strong>
                                        <?php
                                        if (!empty($group['accessories'])) {
                                            echo esc_html(sprintf(
                                                __('%d prodotti selezionati', 'woo-product-addons-jojo'),
                                                count($group['accessories'])
                                            ));
                                        } else {
                                            echo esc_html__('Nessun accessorio selezionato', 'woo-product-addons-jojo');
                                        }
                                        ?>
                                    </div>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                    
                    <button type="button" class="button button-primary add-new-group"><?php echo esc_html__('Aggiungi nuovo gruppo', 'woo-product-addons-jojo'); ?></button>
                </div>
                
                <div id="wpaj-group-form-container" style="display: none;">
                    <h2 id="wpaj-group-form-title"><?php echo esc_html__('Nuovo gruppo', 'woo-product-addons-jojo'); ?></h2>
                    
                    <form id="wpaj-group-form" method="post" action="<?php echo admin_url('admin-post.php'); ?>">
                        <input type="hidden" name="action" value="wpaj_save_group_form">
                        <input type="hidden" id="group_id" name="group_id" value="">
                        <?php wp_nonce_field('wpaj_save_group', 'wpaj_group_nonce'); ?>
                        
                        <div class="form-field">
                            <label for="group_name"><?php echo esc_html__('Nome del gruppo', 'woo-product-addons-jojo'); ?></label>
                            <input type="text" id="group_name" name="group_name" required>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_description"><?php echo esc_html__('Descrizione', 'woo-product-addons-jojo'); ?></label>
                            <textarea id="group_description" name="group_description" rows="3"></textarea>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_categories"><?php echo esc_html__('Categorie di prodotti', 'woo-product-addons-jojo'); ?></label>
                            <select id="group_categories" name="group_categories[]" multiple="multiple" class="wpaj-select2">
                                <?php foreach ($product_categories as $category) : ?>
                                    <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description"><?php echo esc_html__('Seleziona le categorie di prodotti a cui applicare questi accessori.', 'woo-product-addons-jojo'); ?></p>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_min_price"><?php echo esc_html__('Prezzo minimo', 'woo-product-addons-jojo'); ?></label>
                            <input type="number" id="group_min_price" name="group_min_price" min="0" step="0.01">
                            <p class="description"><?php echo esc_html__('Mostra accessori solo se il prodotto principale ha un prezzo maggiore o uguale a questo valore. Lascia vuoto per nessun limite minimo.', 'woo-product-addons-jojo'); ?></p>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_max_price"><?php echo esc_html__('Prezzo massimo', 'woo-product-addons-jojo'); ?></label>
                            <input type="number" id="group_max_price" name="group_max_price" min="0" step="0.01">
                            <p class="description"><?php echo esc_html__('Mostra accessori solo se il prodotto principale ha un prezzo minore o uguale a questo valore. Lascia vuoto per nessun limite massimo.', 'woo-product-addons-jojo'); ?></p>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_display_mode"><?php echo esc_html__('Modalità di visualizzazione', 'woo-product-addons-jojo'); ?></label>
                            <select id="group_display_mode" name="group_display_mode">
                                <option value="half-width"><?php echo esc_html__('Half Width (due per riga)', 'woo-product-addons-jojo'); ?></option>
                                <option value="full-width"><?php echo esc_html__('Full Width (uno per riga)', 'woo-product-addons-jojo'); ?></option>
                            </select>
                            <p class="description"><?php echo esc_html__('Imposta come gli accessori vengono visualizzati nella pagina del prodotto.', 'woo-product-addons-jojo'); ?></p>
                        </div>
                        
                        <div class="form-field">
                            <label for="group_accessories_search"><?php echo esc_html__('Cerca prodotti accessori', 'woo-product-addons-jojo'); ?></label>
                            <input type="text" id="group_accessories_search" placeholder="<?php echo esc_attr__('Digita per cercare prodotti...', 'woo-product-addons-jojo'); ?>">
                            
                            <div id="group_accessories_container" class="product_accessories_container">
                                <p class="no-accessories-message"><?php echo esc_html__('Nessun accessorio selezionato.', 'woo-product-addons-jojo'); ?></p>
                                <div id="group_selected_accessories"></div>
                            </div>
                            <input type="hidden" id="group_accessories" name="group_accessories" value="">
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="button button-primary"><?php echo esc_html__('Salva gruppo', 'woo-product-addons-jojo'); ?></button>
                            <button type="button" class="button cancel-group"><?php echo esc_html__('Annulla', 'woo-product-addons-jojo'); ?></button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Renderizza la pagina delle impostazioni
     */
    public function render_settings_page() {
        // Ottieni le impostazioni generali
        $settings = get_option($this->settings_option_name, array());
        $redirect_to_cart = isset($settings['redirect_to_cart']) ? $settings['redirect_to_cart'] : 1;
        
        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('Gestione Prodotti Accessori', 'woo-product-addons-jojo'); ?></h1>
            
            <div class="nav-tab-wrapper">
                <a href="<?php echo admin_url('edit.php?post_type=product&page=wpaj-accessory-groups'); ?>" class="nav-tab"><?php echo esc_html__('Gruppi di Accessori', 'woo-product-addons-jojo'); ?></a>
                <a href="<?php echo admin_url('edit.php?post_type=product&page=wpaj-accessory-settings'); ?>" class="nav-tab nav-tab-active"><?php echo esc_html__('Impostazioni', 'woo-product-addons-jojo'); ?></a>
            </div>
            
            <form id="wpaj-settings-form" method="post" action="options.php">
                <?php settings_fields('wpaj_settings_option_group'); ?>
                
                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php echo esc_html__('Reindirizzamento al Carrello', 'woo-product-addons-jojo'); ?></th>
                        <td>
                            <label for="<?php echo esc_attr($this->settings_option_name); ?>[redirect_to_cart]">
                                <input type="checkbox" id="<?php echo esc_attr($this->settings_option_name); ?>[redirect_to_cart]" name="<?php echo esc_attr($this->settings_option_name); ?>[redirect_to_cart]" value="1" <?php checked(1, $redirect_to_cart); ?>>
                                <?php echo esc_html__('Reindirizza automaticamente al carrello dopo l\'aggiunta dei prodotti', 'woo-product-addons-jojo'); ?>
                            </label>
                            <p class="description"><?php echo esc_html__('Se disabilitato, i prodotti verranno aggiunti al carrello senza reindirizzare l\'utente.', 'woo-product-addons-jojo'); ?></p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(__('Salva Impostazioni', 'woo-product-addons-jojo')); ?>
            </form>
        </div>
        <?php
    }

    /**
     * Gestisce la ricerca AJAX dei prodotti
     */
    public function ajax_search_products() {
        // Verifica il nonce
        check_ajax_referer('wpaj_groups_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(-1);
        }
        
        $term = isset($_GET['term']) ? sanitize_text_field($_GET['term']) : '';
        
        if (empty($term)) {
            wp_die();
        }
        
        $args = array(
            'post_type' => 'product',
            'post_status' => array('publish', 'private'), // Includi prodotti pubblici e privati
            'posts_per_page' => 10,
            's' => $term,
            'fields' => 'ids'
        );
        
        $query = new WP_Query($args);
        $products = array();
        
        if ($query->have_posts()) {
            foreach ($query->posts as $product_id) {
                $product = wc_get_product($product_id);
                
                if (!$product) {
                    continue;
                }
                
                $image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'thumbnail');
                $image_url = $image ? $image[0] : wc_placeholder_img_src();
                
                $products[] = array(
                    'id' => $product_id,
                    'label' => sprintf(
                        '<div class="product-suggestion"><div class="product-suggestion-image"><img src="%s" alt="%s"></div><div class="product-suggestion-info"><div class="product-suggestion-name">%s</div><div class="product-suggestion-price">%s</div></div></div>',
                        esc_url($image_url),
                        esc_attr($product->get_name()),
                        esc_html($product->get_name()),
                        $product->get_price_html()
                    ),
                    'value' => $product->get_name(),
                    'name' => $product->get_name(),
                    'price_html' => $product->get_price_html(),
                    'image_url' => $image_url
                );
            }
        }
        
        wp_send_json($products);
    }

    /**
     * Gestisce il salvataggio AJAX di un gruppo
     */
    public function ajax_save_group() {
        // Verifica il nonce
        check_ajax_referer('wpaj_groups_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(-1);
        }
        
        $group_id = isset($_POST['group_id']) ? sanitize_text_field($_POST['group_id']) : '';
        $name = isset($_POST['name']) ? sanitize_text_field($_POST['name']) : '';
        $description = isset($_POST['description']) ? wp_kses_post($_POST['description']) : '';
        $categories = isset($_POST['categories']) ? array_map('absint', $_POST['categories']) : array();
        $accessories = isset($_POST['accessories']) ? array_map('absint', $_POST['accessories']) : array();
        $min_price = isset($_POST['min_price']) ? sanitize_text_field($_POST['min_price']) : '';
        $max_price = isset($_POST['max_price']) ? sanitize_text_field($_POST['max_price']) : '';
        $display_mode = isset($_POST['display_mode']) && in_array($_POST['display_mode'], array('half-width', 'full-width')) ? $_POST['display_mode'] : 'half-width';
        
        if (empty($name)) {
            wp_send_json_error(array(
                'message' => __('Il nome del gruppo è obbligatorio.', 'woo-product-addons-jojo')
            ));
            return;
        }
        
        // Ottieni i gruppi esistenti
        $groups = get_option($this->option_name, array());
        
        // Se non è stato fornito un ID, genera un nuovo ID univoco
        if (empty($group_id)) {
            $group_id = 'group_' . time() . '_' . mt_rand(1000, 9999);
        }
        
        // Aggiorna o aggiungi il gruppo
        $groups[$group_id] = array(
            'name' => $name,
            'description' => $description,
            'categories' => $categories,
            'accessories' => $accessories,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'display_mode' => $display_mode
        );
        
        // Salva i gruppi aggiornati
        update_option($this->option_name, $groups);
        
        wp_send_json_success(array(
            'group_id' => $group_id,
            'message' => __('Gruppo salvato con successo.', 'woo-product-addons-jojo')
        ));
    }

    /**
     * Gestisce l'eliminazione AJAX di un gruppo
     */
    public function ajax_delete_group() {
        // Verifica il nonce
        check_ajax_referer('wpaj_groups_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(-1);
        }
        
        $group_id = isset($_POST['group_id']) ? sanitize_text_field($_POST['group_id']) : '';
        
        if (empty($group_id)) {
            wp_send_json_error(array(
                'message' => __('ID gruppo non valido.', 'woo-product-addons-jojo')
            ));
            return;
        }
        
        // Ottieni i gruppi esistenti
        $groups = get_option($this->option_name, array());
        
        // Verifica che il gruppo esista
        if (!isset($groups[$group_id])) {
            wp_send_json_error(array(
                'message' => __('Gruppo non trovato.', 'woo-product-addons-jojo')
            ));
            return;
        }
        
        // Rimuovi il gruppo
        unset($groups[$group_id]);
        
        // Salva i gruppi aggiornati
        update_option($this->option_name, $groups);
        
        wp_send_json_success(array(
            'message' => __('Gruppo eliminato con successo.', 'woo-product-addons-jojo')
        ));
    }

    /**
     * Gestisce la richiesta AJAX per ottenere i dettagli di un gruppo
     */
    public function ajax_get_group() {
        // Verifica il nonce
        check_ajax_referer('wpaj_groups_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(-1);
        }
        
        $group_id = isset($_GET['group_id']) ? sanitize_text_field($_GET['group_id']) : '';
        
        if (empty($group_id)) {
            wp_send_json_error(array('message' => __('ID gruppo non valido.', 'woo-product-addons-jojo')));
            return;
        }
        
        // Ottieni i gruppi
        $groups = get_option($this->option_name, array());
        
        if (!isset($groups[$group_id])) {
            wp_send_json_error(array('message' => __('Gruppo non trovato.', 'woo-product-addons-jojo')));
            return;
        }
        
        $group = $groups[$group_id];
        
        wp_send_json_success(array(
            'id' => $group_id,
            'name' => $group['name'],
            'description' => $group['description'],
            'categories' => isset($group['categories']) ? $group['categories'] : array(),
            'accessories' => isset($group['accessories']) ? $group['accessories'] : array(),
            'min_price' => isset($group['min_price']) ? $group['min_price'] : '',
            'max_price' => isset($group['max_price']) ? $group['max_price'] : '',
            'display_mode' => isset($group['display_mode']) ? $group['display_mode'] : 'half-width'
        ));
    }

    /**
     * Gestisce la richiesta AJAX per ottenere i dettagli dei prodotti
     */
    public function ajax_get_products_details() {
        // Verifica il nonce
        check_ajax_referer('wpaj_groups_nonce', 'nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(-1);
        }
        
        $product_ids = isset($_POST['product_ids']) ? (array) $_POST['product_ids'] : array();
        
        if (empty($product_ids)) {
            wp_send_json_error(array(
                'message' => __('Nessun prodotto specificato.', 'woo-product-addons-jojo')
            ));
            return;
        }
        
        $products = array();
        
        foreach ($product_ids as $product_id) {
            $product = wc_get_product($product_id);
            
            if (!$product) {
                continue;
            }
            
            $image = wp_get_attachment_image_src(get_post_thumbnail_id($product_id), 'thumbnail');
            $image_url = $image ? $image[0] : wc_placeholder_img_src();
            
            $products[] = array(
                'id' => $product_id,
                'name' => $product->get_name(),
                'price_html' => $product->get_price_html(),
                'image_url' => $image_url
            );
        }
        
        wp_send_json_success($products);
    }

    /**
     * Ottiene gli accessori per un prodotto in base alle sue categorie
     *
     * @param int $product_id ID del prodotto
     * @return array Array di accessori organizzati per gruppo
     */
    public function get_accessories_for_product($product_id) {
        $product = wc_get_product($product_id);
        
        if (!$product) {
            return array();
        }
        
        // Ottieni le categorie del prodotto
        $product_categories = wc_get_product_term_ids($product_id, 'product_cat');
        
        if (empty($product_categories)) {
            return array();
        }
        
        // Ottieni tutti i gruppi
        $groups = get_option($this->option_name, array());
        
        if (empty($groups)) {
            return array();
        }
        
        $accessories_by_group = array();
        
        // Ottieni il prezzo del prodotto
        $product_price = $product->get_price();
        
        // Cerca gruppi che contengono le categorie del prodotto
        foreach ($groups as $group_id => $group) {
            if (empty($group['categories']) || empty($group['accessories'])) {
                continue;
            }
            
            // Verifica se almeno una categoria del prodotto è presente nel gruppo
            $intersect = array_intersect($product_categories, $group['categories']);
            
            if (!empty($intersect)) {
                // Verifica se il prezzo del prodotto rientra nel range definito
                $min_price = isset($group['min_price']) && $group['min_price'] !== '' ? floatval($group['min_price']) : false;
                $max_price = isset($group['max_price']) && $group['max_price'] !== '' ? floatval($group['max_price']) : false;
                
                // Se entrambi i campi di prezzo sono vuoti, mostra il gruppo indipendentemente dal prezzo
                if ($min_price === false && $max_price === false) {
                    // Nessun filtro di prezzo, mostra il gruppo
                } 
                // Altrimenti, se c'è almeno un filtro di prezzo, verifica che il prodotto rientri nel range
                else if (($min_price !== false && $product_price < $min_price) || 
                    ($max_price !== false && $product_price > $max_price)) {
                    continue; // Il prezzo del prodotto non rientra nel range
                }
                
                // Rimuovi il prodotto stesso dagli accessori se presente
                $accessories = array_diff($group['accessories'], array($product_id));
                
                if (!empty($accessories)) {
                    $accessories_by_group[$group_id] = array(
                        'name' => $group['name'],
                        'description' => $group['description'],
                        'accessories' => $accessories,
                        'display_mode' => isset($group['display_mode']) ? $group['display_mode'] : 'half-width'
                    );
                }
            }
        }
        
        return $accessories_by_group;
    }

    /**
     * Verifica se il reindirizzamento al carrello è attivo
     *
     * @return bool True se il reindirizzamento è attivo, false altrimenti
     */
    public function is_redirect_to_cart_enabled() {
        $options = get_option($this->settings_option_name, array());
        return isset($options['redirect_to_cart']) ? (bool) $options['redirect_to_cart'] : true;
    }
    
    /**
     * Gestisce il salvataggio del form tramite POST
     */
    public function handle_save_group_form() {
        // Verifica il nonce
        check_admin_referer('wpaj_save_group', 'wpaj_group_nonce');
        
        // Verifica i permessi
        if (!current_user_can('manage_woocommerce')) {
            wp_die(__('Non hai i permessi per eseguire questa azione.', 'woo-product-addons-jojo'));
        }
        
        $group_id = isset($_POST['group_id']) ? sanitize_text_field($_POST['group_id']) : '';
        $name = isset($_POST['group_name']) ? sanitize_text_field($_POST['group_name']) : '';
        $description = isset($_POST['group_description']) ? wp_kses_post($_POST['group_description']) : '';
        $categories = isset($_POST['group_categories']) ? array_map('absint', $_POST['group_categories']) : array();
        $accessories = isset($_POST['group_accessories']) ? sanitize_text_field($_POST['group_accessories']) : '';
        $min_price = isset($_POST['group_min_price']) ? sanitize_text_field($_POST['group_min_price']) : '';
        $max_price = isset($_POST['group_max_price']) ? sanitize_text_field($_POST['group_max_price']) : '';
        $display_mode = isset($_POST['group_display_mode']) && in_array($_POST['group_display_mode'], array('half-width', 'full-width')) ? $_POST['group_display_mode'] : 'half-width';
        
        // Converti la stringa di accessori in array
        $accessories_array = !empty($accessories) ? explode(',', $accessories) : array();
        $accessories_array = array_map('absint', $accessories_array);
        
        if (empty($name)) {
            // Reindirizza con un messaggio di errore
            wp_redirect(add_query_arg(
                array(
                    'page' => 'wpaj-accessory-groups',
                    'error' => 'empty_name'
                ),
                admin_url('edit.php?post_type=product')
            ));
            exit;
        }
        
        // Ottieni i gruppi esistenti
        $groups = get_option($this->option_name, array());
        
        // Se non è stato fornito un ID, genera un nuovo ID univoco
        if (empty($group_id)) {
            $group_id = 'group_' . time() . '_' . mt_rand(1000, 9999);
        }
        
        // Aggiorna o aggiungi il gruppo
        $groups[$group_id] = array(
            'name' => $name,
            'description' => $description,
            'categories' => $categories,
            'accessories' => $accessories_array,
            'min_price' => $min_price,
            'max_price' => $max_price,
            'display_mode' => $display_mode
        );
        
        // Salva i gruppi aggiornati
        update_option($this->option_name, $groups);
        
        // Reindirizza alla pagina dei gruppi con un messaggio di successo
        wp_redirect(add_query_arg(
            array(
                'page' => 'wpaj-accessory-groups',
                'message' => 'group_saved'
            ),
            admin_url('edit.php?post_type=product')
        ));
        exit;
    }
} 