/**
 * Script JavaScript per la pagina di gestione dei gruppi di accessori
 */
(function($) {
    'use strict';

    // Variabili globali
    var selectedAccessories = {};
    var currentGroupId = '';

    // Inizializza il plugin quando il documento è pronto
    $(document).ready(function() {
        // Inizializza Select2 per le categorie
        if ($.fn.select2) {
            $('.wpaj-select2').select2({
                placeholder: wpaj_groups_params.i18n.select_categories
            });
        }

        // Inizializza l'autocomplete per la ricerca dei prodotti
        initProductSearch();
        
        // Inizializza la gestione degli eventi per i prodotti selezionati
        initSelectedProductsEvents();
        
        // Inizializza gli eventi per i pulsanti di azione
        initActionButtons();
        
        // Inizializza la gestione del form
        initFormHandling();
    });

    /**
     * Inizializza l'autocomplete per la ricerca dei prodotti
     */
    function initProductSearch() {
        $('#group_accessories_search').autocomplete({
            source: function(request, response) {
                $.ajax({
                    url: wpaj_groups_params.ajax_url,
                    dataType: 'json',
                    data: {
                        term: request.term,
                        action: 'wpaj_search_products_for_group',
                        nonce: wpaj_groups_params.nonce
                    },
                    success: function(data) {
                        if (data.length === 0) {
                            response([{
                                label: wpaj_groups_params.i18n.no_products,
                                value: '',
                                disabled: true
                            }]);
                        } else {
                            response(data);
                        }
                    },
                    error: function() {
                        response([{
                            label: wpaj_groups_params.i18n.error,
                            value: '',
                            disabled: true
                        }]);
                    }
                });
            },
            minLength: 2,
            select: function(event, ui) {
                if (ui.item.disabled) {
                    event.preventDefault();
                    return;
                }
                
                addSelectedProduct(ui.item);
                $(this).val('');
                return false;
            },
            open: function(event, ui) {
                $('.ui-autocomplete').css('width', $(this).outerWidth() + 'px');
            }
        }).data('ui-autocomplete')._renderItem = function(ul, item) {
            if (item.disabled) {
                return $('<li class="ui-state-disabled">')
                    .append($('<div>').html(item.label))
                    .appendTo(ul);
            }
            
            return $('<li>')
                .append($('<div>').html(item.label))
                .appendTo(ul);
        };
    }

    /**
     * Aggiunge un prodotto alla lista dei prodotti selezionati
     *
     * @param {Object} product Il prodotto da aggiungere
     */
    function addSelectedProduct(product) {
        // Verifica se il prodotto è già stato selezionato
        if (selectedAccessories[product.id]) {
            return;
        }
        
        // Aggiungi il prodotto all'oggetto dei prodotti selezionati
        selectedAccessories[product.id] = product;
        
        // Crea l'elemento HTML per il prodotto
        var productHtml = '<div class="selected-product" data-product-id="' + product.id + '">' +
            '<div class="product-image"><img src="' + product.image_url + '" alt="' + product.name + '"></div>' +
            '<div class="product-info">' +
            '<div class="product-name">' + product.name + '</div>' +
            '<div class="product-price">' + product.price_html + '</div>' +
            '</div>' +
            '<a class="remove-product dashicons dashicons-no-alt" title="' + wpaj_groups_params.i18n.remove + '"></a>' +
            '</div>';
        
        // Aggiungi il prodotto alla lista
        $('#group_selected_accessories').append(productHtml);
        
        // Nascondi il messaggio "nessun accessorio"
        $('.no-accessories-message').hide();
        
        // Aggiorna il campo nascosto
        updateHiddenField();
    }

    /**
     * Inizializza la gestione degli eventi per i prodotti selezionati
     */
    function initSelectedProductsEvents() {
        // Gestisci la rimozione di un prodotto
        $(document).on('click', '.remove-product', function() {
            var productId = $(this).closest('.selected-product').data('product-id');
            
            // Rimuovi il prodotto dall'oggetto dei prodotti selezionati
            delete selectedAccessories[productId];
            
            // Rimuovi l'elemento HTML
            $(this).closest('.selected-product').remove();
            
            // Mostra il messaggio "nessun accessorio" se non ci sono più prodotti
            if ($.isEmptyObject(selectedAccessories)) {
                $('.no-accessories-message').show();
            }
            
            // Aggiorna il campo nascosto
            updateHiddenField();
        });
    }

    /**
     * Aggiorna il campo nascosto con i prodotti selezionati
     */
    function updateHiddenField() {
        var accessoryIds = Object.keys(selectedAccessories);
        $('#group_accessories').val(accessoryIds.join(','));
    }

    /**
     * Inizializza gli eventi per i pulsanti di azione
     */
    function initActionButtons() {
        // Gestisci il click sul pulsante "Aggiungi nuovo gruppo"
        $('.add-new-group').on('click', function() {
            resetForm();
            $('#wpaj-group-form-title').text(wpaj_groups_params.i18n.new_group);
            $('#wpaj-group-form-container').show();
            $('.wpaj-groups-list').hide();
        });
        
        // Gestisci il click sul pulsante "Modifica"
        $(document).on('click', '.edit-group', function() {
            var groupId = $(this).closest('.wpaj-group-item').data('group-id');
            editGroup(groupId);
        });
        
        // Gestisci il click sul pulsante "Elimina"
        $(document).on('click', '.delete-group', function() {
            var groupId = $(this).closest('.wpaj-group-item').data('group-id');
            
            if (confirm(wpaj_groups_params.i18n.confirm_delete)) {
                deleteGroup(groupId);
            }
        });
        
        // Gestisci il click sul pulsante "Annulla"
        $('.cancel-group').on('click', function() {
            $('#wpaj-group-form-container').hide();
            $('.wpaj-groups-list').show();
        });
    }

    /**
     * Inizializza la gestione del form
     */
    function initFormHandling() {
        var $ = jQuery;
        
        // Gestione dell'aggiunta di un nuovo gruppo
        $('#wpaj-add-group-form').on('submit', function(e) {
            e.preventDefault();
            
            var groupName = $('#group_name').val();
            var groupDescription = $('#group_description').val();
            
            if (!groupName) {
                alert(wpaj_groups_params.i18n.empty_name);
                return;
            }
            
            // Invia la richiesta AJAX per aggiungere il gruppo
            $.ajax({
                url: wpaj_groups_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpaj_add_group',
                    nonce: wpaj_groups_params.nonce,
                    name: groupName,
                    description: groupDescription
                },
                beforeSend: function() {
                    $('#wpaj-add-group-submit').prop('disabled', true);
                    $('#wpaj-add-group-submit').val(wpaj_groups_params.i18n.adding);
                },
                success: function(response) {
                    if (response.success) {
                        // Ricarica la pagina per mostrare il nuovo gruppo
                        location.reload();
                    } else {
                        alert(response.data.message || wpaj_groups_params.i18n.error);
                    }
                },
                error: function() {
                    alert(wpaj_groups_params.i18n.error);
                },
                complete: function() {
                    $('#wpaj-add-group-submit').prop('disabled', false);
                    $('#wpaj-add-group-submit').val(wpaj_groups_params.i18n.add_group);
                }
            });
        });
        
        // Gestione del click sul pulsante "Salva gruppo"
        $(document).on('click', '#save-group-button', function(e) {
            e.preventDefault();
            // console.log('Save button clicked');
            
            var formData = {
                group_id: $('#group_id').val(),
                name: $('#group_name').val(),
                description: $('#group_description').val(),
                categories: $('#group_categories').val() || [],
                accessories: $('#group_accessories').val(),
                min_price: $('#group_min_price').val(),
                max_price: $('#group_max_price').val(),
                display_mode: $('#group_display_mode').val()
            };
            
            if (!formData.name) {
                alert(wpaj_groups_params.i18n.empty_name);
                return;
            }
            
            saveGroup(formData);
            return false;
        });
        
        // Gestione dell'eliminazione di un gruppo
        $('.wpaj-delete-group').on('click', function(e) {
            e.preventDefault();
            
            if (!confirm(wpaj_groups_params.i18n.confirm_delete)) {
                return;
            }
            
            var groupId = $(this).data('group-id');
            
            // Invia la richiesta AJAX per eliminare il gruppo
            $.ajax({
                url: wpaj_groups_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpaj_delete_group',
                    nonce: wpaj_groups_params.nonce,
                    group_id: groupId
                },
                beforeSend: function() {
                    // Disabilita il pulsante di eliminazione
                    $('.wpaj-delete-group[data-group-id="' + groupId + '"]').prop('disabled', true);
                },
                success: function(response) {
                    if (response.success) {
                        // Ricarica la pagina per aggiornare l'elenco dei gruppi
                        location.reload();
                    } else {
                        alert(response.data.message || wpaj_groups_params.i18n.error);
                    }
                },
                error: function() {
                    alert(wpaj_groups_params.i18n.error);
                },
                complete: function() {
                    // Riabilita il pulsante di eliminazione
                    $('.wpaj-delete-group[data-group-id="' + groupId + '"]').prop('disabled', false);
                }
            });
        });
        
        // Gestione della selezione dei prodotti
        $('.wpaj-product-search').select2({
            ajax: {
                url: wpaj_groups_params.ajax_url,
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        action: 'wpaj_search_products',
                        nonce: wpaj_groups_params.nonce,
                        term: params.term
                    };
                },
                processResults: function(data) {
                    return {
                        results: data.data
                    };
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: wpaj_groups_params.i18n.search_products
        });
        
        // Gestione del salvataggio dei prodotti accessori
        $('.wpaj-save-accessories').on('click', function(e) {
            e.preventDefault();
            
            var groupId = $(this).data('group-id');
            var productIds = $('#accessories_' + groupId).val();
            
            // Invia la richiesta AJAX per salvare i prodotti accessori
            $.ajax({
                url: wpaj_groups_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'wpaj_save_accessories',
                    nonce: wpaj_groups_params.nonce,
                    group_id: groupId,
                    product_ids: productIds
                },
                beforeSend: function() {
                    // Disabilita il pulsante di salvataggio
                    $('.wpaj-save-accessories[data-group-id="' + groupId + '"]').prop('disabled', true);
                    $('.wpaj-save-accessories[data-group-id="' + groupId + '"]').val(wpaj_groups_params.i18n.saving);
                },
                success: function(response) {
                    if (response.success) {
                        alert(wpaj_groups_params.i18n.saved);
                    } else {
                        alert(response.data.message || wpaj_groups_params.i18n.error);
                    }
                },
                error: function() {
                    alert(wpaj_groups_params.i18n.error);
                },
                complete: function() {
                    // Riabilita il pulsante di salvataggio
                    $('.wpaj-save-accessories[data-group-id="' + groupId + '"]').prop('disabled', false);
                    $('.wpaj-save-accessories[data-group-id="' + groupId + '"]').val(wpaj_groups_params.i18n.save);
                }
            });
        });
    }

    /**
     * Resetta il form
     */
    function resetForm() {
        $('#group_id').val('');
        $('#group_name').val('');
        $('#group_description').val('');
        $('#group_categories').val([]).trigger('change');
        $('#group_accessories').val('');
        $('#group_selected_accessories').empty();
        $('.no-accessories-message').show();
        
        selectedAccessories = {};
        currentGroupId = '';
    }

    /**
     * Carica i dati di un gruppo nel form per la modifica
     *
     * @param {string} groupId L'ID del gruppo da modificare
     */
    function editGroup(groupId) {
        // Effettua una richiesta AJAX per ottenere i dati del gruppo
        $.ajax({
            url: wpaj_groups_params.ajax_url,
            type: 'GET',
            dataType: 'json',
            data: {
                action: 'wpaj_get_accessory_group',
                nonce: wpaj_groups_params.nonce,
                group_id: groupId
            },
            success: function(response) {
                if (response.success) {
                    var group = response.data;
                    
                    // Imposta l'ID del gruppo corrente
                    currentGroupId = groupId;
                    
                    // Imposta i valori del form
                    $('#group_id').val(group.id);
                    $('#group_name').val(group.name);
                    $('#group_description').val(group.description);
                    
                    // Seleziona le categorie
                    if ($.fn.select2) {
                        $('#group_categories').val(group.categories).trigger('change');
                    } else {
                        $('#group_categories').val(group.categories);
                    }
                    
                    // Imposta i campi per il range di prezzo
                    $('#group_min_price').val(group.min_price || '');
                    $('#group_max_price').val(group.max_price || '');
                    
                    // Imposta la modalità di visualizzazione
                    $('#group_display_mode').val(group.display_mode || 'half-width');
                    
                    // Svuota il contenitore degli accessori selezionati
                    $('#group_selected_accessories').empty();
                    
                    // Aggiungi i prodotti selezionati
                    if (group.accessories && group.accessories.length > 0) {
                        $('.no-accessories-message').hide();
                        
                        // Carica i dettagli dei prodotti
                        $.ajax({
                            url: wpaj_groups_params.ajax_url,
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                action: 'wpaj_get_products_details',
                                nonce: wpaj_groups_params.nonce,
                                product_ids: group.accessories
                            },
                            success: function(productsResponse) {
                                if (productsResponse.success) {
                                    var products = productsResponse.data;
                                    
                                    // Aggiungi ogni prodotto alla lista
                                    $.each(products, function(index, product) {
                                        addSelectedProduct(product);
                                    });
                                }
                            }
                        });
                    } else {
                        $('.no-accessories-message').show();
                    }
                    
                    // Mostra il form e nascondi la lista
                    $('#wpaj-group-form-title').text(wpaj_groups_params.i18n.edit_group);
                    $('#wpaj-group-form-container').show();
                    $('.wpaj-groups-list').hide();
                }
            }
        });
    }

    /**
     * Salva un gruppo
     *
     * @param {Object} formData I dati del form
     */
    function saveGroup(formData) {
        // console.log('saveGroup called with data:', formData);
        
        $.ajax({
            url: wpaj_groups_params.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'wpaj_save_accessory_group',
                nonce: wpaj_groups_params.nonce,
                group_id: formData.group_id,
                name: formData.name,
                description: formData.description,
                categories: formData.categories,
                accessories: formData.accessories,
                min_price: formData.min_price,
                max_price: formData.max_price,
                display_mode: formData.display_mode
            },
            beforeSend: function() {
                // console.log('AJAX request being sent to:', wpaj_groups_params.ajax_url);
            },
            success: function(response) {
                // console.log('AJAX response:', response);
                if (response.success) {
                    // Mostra un messaggio di successo
                    alert(wpaj_groups_params.i18n.group_saved);
                    
                    // Ricarica la pagina per mostrare i cambiamenti
                    window.location.reload();
                } else {
                    // Mostra un messaggio di errore
                    alert(response.data.message);
                }
            },
            error: function() {
                // Mostra un messaggio di errore generico
                alert(wpaj_groups_params.i18n.error_saving);
            }
        });
    }

    /**
     * Elimina un gruppo
     *
     * @param {string} groupId L'ID del gruppo da eliminare
     */
    function deleteGroup(groupId) {
        $.ajax({
            url: wpaj_groups_params.ajax_url,
            type: 'POST',
            dataType: 'json',
            data: {
                action: 'wpaj_delete_accessory_group',
                nonce: wpaj_groups_params.nonce,
                group_id: groupId
            },
            success: function(response) {
                if (response.success) {
                    // Mostra un messaggio di successo
                    alert(wpaj_groups_params.i18n.group_deleted);
                    
                    // Rimuovi l'elemento dalla lista
                    $('.wpaj-group-item[data-group-id="' + groupId + '"]').remove();
                    
                    // Se non ci sono più gruppi, mostra il messaggio
                    if ($('.wpaj-group-item').length === 0) {
                        $('.wpaj-groups').before('<p>' + wpaj_groups_params.i18n.no_groups + '</p>');
                    }
                } else {
                    // Mostra un messaggio di errore
                    alert(response.data.message);
                }
            },
            error: function() {
                // Mostra un messaggio di errore generico
                alert(wpaj_groups_params.i18n.error_deleting);
            }
        });
    }

})(jQuery); 